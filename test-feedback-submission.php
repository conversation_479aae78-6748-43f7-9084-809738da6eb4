<?php
/**
 * Test Feedback Submission
 * 
 * This script tests the feedback submission functionality
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Get the database connection
global $wpdb;

echo "<h1>Testing Feedback Submission</h1>";

// Test 1: Check if feedback submission endpoint works
echo "<h2>Test 1: Feedback Submission Endpoint</h2>";

if (class_exists('QKB_ML_Handler')) {
    $ml_handler = QKB_ML_Handler::get_instance();
    
    // Simulate a feedback submission
    $_POST['message_id'] = 'test_' . time();
    $_POST['feedback'] = 1; // Positive feedback
    $_POST['query'] = 'Test query for feedback submission';
    $_POST['response'] = 'Test response for feedback submission';
    $_POST['assistant_id'] = 1;
    $_POST['nonce'] = wp_create_nonce('qkb_ajax_nonce');
    
    echo "<p>Simulating feedback submission...</p>";
    
    // Capture output
    ob_start();
    try {
        $ml_handler->handle_feedback();
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    }
    $output = ob_get_clean();
    
    if (strpos($output, 'success') !== false || empty($output)) {
        echo "<p style='color: green;'>✓ Feedback submission appears to be working</p>";
        
        // Check if the feedback was actually stored
        $table_name = $wpdb->prefix . 'qkb_ml_interactions';
        $recent_feedback = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE message_id = %s ORDER BY created_at DESC LIMIT 1",
                $_POST['message_id']
            )
        );
        
        if ($recent_feedback) {
            echo "<p style='color: green;'>✓ Feedback was stored in database</p>";
            echo "<p>Stored feedback details:</p>";
            echo "<ul>";
            echo "<li>Message ID: " . $recent_feedback->message_id . "</li>";
            echo "<li>Query: " . $recent_feedback->query . "</li>";
            echo "<li>Response: " . $recent_feedback->response . "</li>";
            echo "<li>Feedback: " . ($recent_feedback->feedback > 0 ? 'Positive' : ($recent_feedback->feedback < 0 ? 'Negative' : 'Neutral')) . "</li>";
            echo "<li>Created: " . $recent_feedback->created_at . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>✗ Feedback was not stored in database</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Feedback submission failed</p>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>✗ QKB_ML_Handler class not found</p>";
}

// Test 2: Check chatbot feedback buttons
echo "<h2>Test 2: Chatbot Integration</h2>";

// Check if chatbot class exists and has feedback functionality
if (class_exists('QKB_Chatbot')) {
    echo "<p style='color: green;'>✓ QKB_Chatbot class exists</p>";
    
    // Check if the chatbot JavaScript includes feedback functionality
    $chatbot_js_path = dirname(__FILE__) . '/assets/js/chatbot.js';
    if (file_exists($chatbot_js_path)) {
        $chatbot_js_content = file_get_contents($chatbot_js_path);
        
        if (strpos($chatbot_js_content, 'submitFeedback') !== false) {
            echo "<p style='color: green;'>✓ Chatbot JavaScript includes feedback functionality</p>";
        } else {
            echo "<p style='color: red;'>✗ Chatbot JavaScript missing feedback functionality</p>";
        }
        
        if (strpos($chatbot_js_content, 'qkb_submit_feedback') !== false) {
            echo "<p style='color: green;'>✓ Chatbot JavaScript includes AJAX action for feedback</p>";
        } else {
            echo "<p style='color: red;'>✗ Chatbot JavaScript missing AJAX action for feedback</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Chatbot JavaScript file not found</p>";
    }
} else {
    echo "<p style='color: red;'>✗ QKB_Chatbot class not found</p>";
}

// Test 3: Check database table structure
echo "<h2>Test 3: Database Table Structure</h2>";

$table_name = $wpdb->prefix . 'qkb_ml_interactions';
if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
    echo "<p style='color: green;'>✓ Feedback table exists</p>";
    
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $required_columns = ['id', 'message_id', 'query', 'response', 'feedback', 'assistant_id', 'created_at'];
    $existing_columns = array_column($columns, 'Field');
    
    $missing_columns = array_diff($required_columns, $existing_columns);
    
    if (empty($missing_columns)) {
        echo "<p style='color: green;'>✓ All required columns exist</p>";
    } else {
        echo "<p style='color: red;'>✗ Missing columns: " . implode(', ', $missing_columns) . "</p>";
    }
    
    // Show current row count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<p>Current feedback records: <strong>$count</strong></p>";
    
} else {
    echo "<p style='color: red;'>✗ Feedback table does not exist</p>";
}

// Test 4: Test AJAX endpoint directly
echo "<h2>Test 4: Direct AJAX Test</h2>";

echo "<div id='ajax-test-results'>";
echo "<p>Testing AJAX endpoint directly...</p>";
echo "<script>
jQuery(document).ready(function($) {
    $.ajax({
        url: '" . admin_url('admin-ajax.php') . "',
        type: 'POST',
        data: {
            action: 'qkb_submit_feedback',
            nonce: '" . wp_create_nonce('qkb_ajax_nonce') . "',
            message_id: 'ajax_test_' + Date.now(),
            feedback: 1,
            query: 'AJAX test query',
            response: 'AJAX test response',
            assistant_id: 1
        },
        success: function(response) {
            $('#ajax-test-results').append('<p style=\"color: green;\">✓ AJAX feedback submission successful</p>');
            console.log('AJAX Success:', response);
        },
        error: function(xhr, status, error) {
            $('#ajax-test-results').append('<p style=\"color: red;\">✗ AJAX feedback submission failed: ' + error + '</p>');
            console.log('AJAX Error:', xhr.responseText);
        }
    });
});
</script>";
echo "</div>";

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to see if feedback submission is working properly.</p>";
echo "<p>If there are any issues, they should be visible in the test results.</p>";

// Add link to analytics page
$admin_url = admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-feedback-analytics');
echo "<p><a href='$admin_url' target='_blank' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>View Feedback Analytics</a></p>";
